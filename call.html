<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover">
    <meta name="theme-color" content="#12091c">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="ECP-IP-CALL">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <link rel="icon" href="ai.ico" type="image/x-icon">
    <link rel="apple-touch-icon" href="ai.ico">
    <title>ECP-IP-CALL</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="contact.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
</head>
<body>
    <!-- Join button -->
    <button id="join-btn" aria-label="Join video call" title="Join ECP-IP-CALL" tabindex="0">
        <i class="fas fa-phone-alt" aria-hidden="true"></i>
        <span>ECP-IP-CALL</span>
    </button>
     
    <!-- Stream Wrapper for video -->
    <div id="stream-wrapper">
        <div id="video-streams" role="main" aria-label="Video call participants"></div>

       <!-- Stream Controls -->
  <div id="stream-controls" role="toolbar" aria-label="Call controls">
    <button id="leave-btn" aria-label="Leave Call" title="Leave Call" tabindex="0">
        <i class="fas fa-sign-out-alt" aria-hidden="true"></i>
        <span class="sr-only">Leave</span>
    </button>
    <button id="mic-btn" aria-label="Toggle Microphone" title="Toggle Microphone" tabindex="0">
        <i class="fas fa-microphone" aria-hidden="true"></i>
        <span class="sr-only">Microphone</span>
    </button>
    <button id="camera-btn" aria-label="Toggle Camera" title="Toggle Camera" tabindex="0">
        <i class="fas fa-video" aria-hidden="true"></i>
        <span class="sr-only">Camera</span>
    </button>
    <button id="switch-camera-btn" aria-label="Switch Camera" title="Switch Camera" tabindex="0">
        <i class="fas fa-sync" aria-hidden="true"></i>
        <span class="sr-only">Switch Camera</span>
    </button>
  </div>
  <a href="javascript:history.back()" class="back-button" title="Go Back" aria-label="Go back to previous page" tabindex="0">
    <i class="fas fa-arrow-left" aria-hidden="true"></i>
</a>

<!-- Theme Toggle Button -->
<button id="theme-toggle" class="theme-toggle" title="Change Theme" aria-label="Open theme selection menu" aria-expanded="false" tabindex="0">
    <i class="fas fa-palette" aria-hidden="true"></i>
</button>

<!-- Theme Selection Menu -->
<div id="theme-menu" class="theme-menu" role="menu" aria-label="Theme selection">
    <div class="theme-option" data-theme="default" role="menuitem" tabindex="0" aria-label="Select Dark Purple theme">
        <div class="theme-preview theme-preview-default" aria-hidden="true"></div>
        <span>Dark Purple</span>
    </div>
    <div class="theme-option" data-theme="ocean" role="menuitem" tabindex="0" aria-label="Select Ocean Blue theme">
        <div class="theme-preview theme-preview-ocean" aria-hidden="true"></div>
        <span>Ocean Blue</span>
    </div>
    <div class="theme-option" data-theme="forest" role="menuitem" tabindex="0" aria-label="Select Forest Green theme">
        <div class="theme-preview theme-preview-forest" aria-hidden="true"></div>
        <span>Forest Green</span>
    </div>
    <div class="theme-option" data-theme="sunset" role="menuitem" tabindex="0" aria-label="Select Sunset Orange theme">
        <div class="theme-preview theme-preview-sunset" aria-hidden="true"></div>
        <span>Sunset Orange</span>
    </div>
    <div class="theme-option" data-theme="light" role="menuitem" tabindex="0" aria-label="Select Light Mode theme">
        <div class="theme-preview theme-preview-light" aria-hidden="true"></div>
        <span>Light Mode</span>
    </div>
    <div class="theme-option" data-theme="brutalist" role="menuitem" tabindex="0" aria-label="Select Neo Brutalist theme">
        <div class="theme-preview theme-preview-brutalist" aria-hidden="true"></div>
        <span>Neo Brutalist</span>
    </div>
</div>
     <script src="AgoraRTC_N-4.7.3.js"></script>
    <script src="contact.js"></script>

</body>
</html>
