const APP_ID = "8f61c340d00d408997dfa023554e1925";
const TOKEN = "007eJxTYAj1UXW5lhe4uu74hP45e05JnOAt592YF6XY9Sziy9bKq/YKDBZpZobJxiYGKQYGKSYGFpaW5ilpiQZGxqamJqmGlkamnaY9GQ2BjAwh5e1MjAwQCOKzMJTn56QxMAAAJhcehA==";
const CHANNEL = "wolf";
const client = AgoraRTC.createClient({ mode: 'rtc', codec: 'vp8' });

let localTracks = [];
let remoteUsers = {};
let currentCameraId = null;  // Track current camera deviceId
let UID = null;
let isJoining = false;  // Track if currently joining
let isJoined = false;   // Track if already joined
let memberCount = 0;    // Track total number of members

let joinAndDisplayLocalStream = async () => {
    try {
        // Set up event listeners only once
        if (!isJoined) {
            client.on('user-published', handleUserJoined);
            client.on('user-left', handleUserLeft);
        }

        UID = await client.join(APP_ID, CHANNEL, TOKEN, null);
        isJoined = true;

        localTracks = await AgoraRTC.createMicrophoneAndCameraTracks();

        let player = `<div class="video-container" id="user-container-${UID}" data-username="You">
                            <div class="video-player" id="user-${UID}"></div>
                      </div>`;
        document.getElementById('video-streams').insertAdjacentHTML('beforeend', player);

        localTracks[1].play(`user-${UID}`);

        await client.publish([localTracks[0], localTracks[1]]);

        // Update member count and grid layout
        memberCount = 1;
        updateGridLayout();

        console.log('Successfully joined and published local stream');
    } catch (error) {
        console.error('Error joining stream:', error);
        isJoining = false;
        isJoined = false;
        throw error;
    }
};

let joinStream = async () => {
    // Prevent multiple join attempts
    if (isJoining || isJoined) {
        console.log('Already joining or joined, ignoring request');
        return;
    }

    try {
        isJoining = true;

        // Disable join button to prevent multiple clicks
        const joinBtn = document.getElementById('join-btn');
        joinBtn.disabled = true;
        joinBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>Joining...</span>';

        await joinAndDisplayLocalStream();

        // Hide join button and show controls
        joinBtn.style.display = 'none';
        document.getElementById('stream-controls').style.display = 'flex';

        console.log('Successfully joined stream');
    } catch (error) {
        console.error('Failed to join stream:', error);

        // Reset button state on error
        const joinBtn = document.getElementById('join-btn');
        joinBtn.disabled = false;
        joinBtn.innerHTML = '<i class="fas fa-phone-alt"></i> <span>ECP-IP-CALL</span>';

        // Show user-friendly error message
        alert('Failed to join the call. Please check your connection and try again.');
    } finally {
        isJoining = false;
    }
};

let handleUserJoined = async (user, mediaType) => {
    remoteUsers[user.uid] = user;
    await client.subscribe(user, mediaType);

    if (mediaType === 'video') {
        let player = document.getElementById(`user-container-${user.uid}`);
        if (player != null) {
            player.remove();
            memberCount--; // Decrease count if replacing existing
        }

        // Generate username (you can customize this logic)
        const username = `User ${user.uid.toString().slice(-4)}`;

        player = `<div class="video-container" id="user-container-${user.uid}" data-username="${username}">
                        <div class="video-player" id="user-${user.uid}"></div>
                 </div>`;
        document.getElementById('video-streams').insertAdjacentHTML('beforeend', player);

        user.videoTrack.play(`user-${user.uid}`);

        // Update member count and grid layout
        memberCount++;
        updateGridLayout();

        console.log(`User ${user.uid} joined. Total members: ${memberCount}`);
    }

    if (mediaType === 'audio') {
        user.audioTrack.play();
    }
};

let handleUserLeft = async (user) => {
    delete remoteUsers[user.uid];
    const userContainer = document.getElementById(`user-container-${user.uid}`);
    if (userContainer) {
        userContainer.remove();
        memberCount--;
        updateGridLayout();
        console.log(`User ${user.uid} left. Total members: ${memberCount}`);
    }
};

let leaveAndRemoveLocalStream = async () => {
    try {
        // Stop and close local tracks
        for (let i = 0; localTracks.length > i; i++) {
            localTracks[i].stop();
            localTracks[i].close();
        }

        // Leave the channel
        await client.leave();

        // Reset state variables
        isJoined = false;
        isJoining = false;
        UID = null;
        localTracks = [];
        remoteUsers = {};
        currentCameraId = null;
        memberCount = 0;

        // Reset UI
        const joinBtn = document.getElementById('join-btn');
        joinBtn.style.display = 'block';
        joinBtn.disabled = false;
        joinBtn.innerHTML = '<i class="fas fa-phone-alt"></i> <span>ECP-IP-CALL</span>';

        document.getElementById('stream-controls').style.display = 'none';
        document.getElementById('video-streams').innerHTML = '';

        console.log('Successfully left the stream');
    } catch (error) {
        console.error('Error leaving stream:', error);

        // Force reset state even if there's an error
        isJoined = false;
        isJoining = false;
        UID = null;
        localTracks = [];
        remoteUsers = {};
        currentCameraId = null;
    }
};

let toggleMic = async (e) => {
    try {
        if (!localTracks[0] || !isJoined) {
            console.log('Microphone track not available');
            return;
        }

        if (localTracks[0].muted) {
            await localTracks[0].setMuted(false);
            e.target.innerHTML = '<i class="fas fa-microphone"></i>';
            e.target.classList.remove('active');
            e.target.setAttribute('aria-label', 'Mute microphone');
            e.target.setAttribute('title', 'Mute microphone');
        } else {
            await localTracks[0].setMuted(true);
            e.target.innerHTML = '<i class="fas fa-microphone-slash"></i>';
            e.target.classList.add('active');
            e.target.setAttribute('aria-label', 'Unmute microphone');
            e.target.setAttribute('title', 'Unmute microphone');
        }
    } catch (error) {
        console.error('Error toggling microphone:', error);
    }
};

let toggleCamera = async (e) => {
    try {
        if (!localTracks[1] || !isJoined) {
            console.log('Camera track not available');
            return;
        }

        if (localTracks[1].muted) {
            await localTracks[1].setMuted(false);
            e.target.innerHTML = '<i class="fas fa-video"></i>';
            e.target.classList.remove('active');
            e.target.setAttribute('aria-label', 'Turn off camera');
            e.target.setAttribute('title', 'Turn off camera');
        } else {
            await localTracks[1].setMuted(true);
            e.target.innerHTML = '<i class="fas fa-video-slash"></i>';
            e.target.classList.add('active');
            e.target.setAttribute('aria-label', 'Turn on camera');
            e.target.setAttribute('title', 'Turn on camera');
        }
    } catch (error) {
        console.error('Error toggling camera:', error);
    }
};

// Switch between front and back cameras
let switchCamera = async () => {
    try {
        if (!localTracks[1] || !isJoined) {
            console.log('Camera track not available or not joined');
            return;
        }

        // Get all video devices (cameras)
        const devices = await AgoraRTC.getDevices();

        // Filter video devices (cameras)
        const videoDevices = devices.filter(device => device.kind === 'videoinput');
        console.log('Video devices found:', videoDevices);

        if (videoDevices.length < 2) {
            console.log('Only one camera found, unable to switch.');
            return;
        }

        // Find the front and back cameras by checking the label
        const frontCamera = videoDevices.find(device => device.label.toLowerCase().includes('front'));
        const backCamera = videoDevices.find(device => device.label.toLowerCase().includes('back'));

        // Determine which camera to switch to
        let nextCamera = null;
        if (currentCameraId === backCamera?.deviceId) {
            nextCamera = frontCamera;
        } else if (currentCameraId === frontCamera?.deviceId) {
            nextCamera = backCamera;
        } else {
            // Default to back camera if none selected yet
            nextCamera = backCamera || videoDevices[1]; // Fallback to second camera
        }

        if (nextCamera) {
            // Stop the current camera and close it
            await localTracks[1].stop();
            await localTracks[1].close();

            // Switch to the new camera
            currentCameraId = nextCamera.deviceId;

            // Create a new camera track for the next camera
            localTracks[1] = await AgoraRTC.createCameraTrack({ cameraId: currentCameraId });

            // Play the new camera stream
            localTracks[1].play(`user-${UID}`);

            // Republish the new camera track
            await client.publish([localTracks[1]]);

            console.log('Successfully switched camera');
        } else {
            console.log('No suitable camera found.');
        }
    } catch (error) {
        console.error('Error switching camera:', error);
    }
};

// Theme Management
let currentTheme = localStorage.getItem('theme') || 'default';

// Apply saved theme on page load
document.addEventListener('DOMContentLoaded', () => {
    applyTheme(currentTheme);
    updateActiveThemeOption();
});

function applyTheme(theme) {
    const body = document.body;

    // Remove all theme classes
    body.classList.remove('theme-ocean', 'theme-forest', 'theme-sunset', 'theme-light', 'theme-brutalist');

    // Apply new theme class (except for default)
    if (theme !== 'default') {
        body.classList.add(`theme-${theme}`);
    }

    currentTheme = theme;
    localStorage.setItem('theme', theme);
}

function updateActiveThemeOption() {
    const themeOptions = document.querySelectorAll('.theme-option');
    themeOptions.forEach(option => {
        option.classList.remove('active');
        if (option.dataset.theme === currentTheme) {
            option.classList.add('active');
        }
    });
}

function toggleThemeMenu() {
    const themeMenu = document.getElementById('theme-menu');
    themeMenu.classList.toggle('show');
}

// Close theme menu when clicking outside
document.addEventListener('click', (e) => {
    const themeToggle = document.getElementById('theme-toggle');
    const themeMenu = document.getElementById('theme-menu');

    if (!themeToggle.contains(e.target) && !themeMenu.contains(e.target)) {
        themeMenu.classList.remove('show');
    }
});

// Utility function to check connection state
function getConnectionState() {
    return {
        isJoining,
        isJoined,
        hasLocalTracks: localTracks.length > 0,
        UID,
        connectionState: client.connectionState,
        memberCount
    };
}

// Update grid layout based on member count
function updateGridLayout() {
    const videoStreams = document.getElementById('video-streams');
    if (videoStreams) {
        videoStreams.setAttribute('data-members', memberCount.toString());

        // Check for overflow and add indicator
        setTimeout(() => {
            checkScrollOverflow();
        }, 100);

        // Add smooth scroll to new members
        if (memberCount > 1) {
            setTimeout(() => {
                const lastContainer = videoStreams.lastElementChild;
                if (lastContainer && lastContainer.classList.contains('video-container')) {
                    lastContainer.scrollIntoView({
                        behavior: 'smooth',
                        block: 'nearest',
                        inline: 'nearest'
                    });
                }
            }, 300);
        }

        console.log(`Grid layout updated for ${memberCount} members`);
    }
}

// Check if video streams container has overflow
function checkScrollOverflow() {
    const videoStreams = document.getElementById('video-streams');
    if (videoStreams) {
        const hasVerticalOverflow = videoStreams.scrollHeight > videoStreams.clientHeight;
        const hasHorizontalOverflow = videoStreams.scrollWidth > videoStreams.clientWidth;

        if (hasVerticalOverflow || hasHorizontalOverflow) {
            videoStreams.classList.add('has-overflow');
        } else {
            videoStreams.classList.remove('has-overflow');
        }
    }
}

// Responsive grid adjustment based on screen size
function adjustGridForScreenSize() {
    const videoStreams = document.getElementById('video-streams');
    const screenWidth = window.innerWidth;

    if (videoStreams && memberCount > 0) {
        let minSize;

        if (screenWidth <= 375) {
            minSize = '120px';
        } else if (screenWidth <= 480) {
            minSize = '140px';
        } else if (screenWidth <= 600) {
            minSize = '150px';
        } else if (screenWidth <= 768) {
            minSize = '160px';
        } else if (screenWidth <= 1024) {
            minSize = '180px';
        } else {
            minSize = '200px';
        }

        // Apply dynamic sizing for auto-fit layouts
        if (memberCount > 9) {
            videoStreams.style.gridTemplateColumns = `repeat(auto-fit, minmax(${minSize}, 1fr))`;
        }
    }
}

// Smooth scroll to specific member
function scrollToMember(uid) {
    const memberContainer = document.getElementById(`user-container-${uid}`);
    if (memberContainer) {
        memberContainer.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
            inline: 'center'
        });
    }
}

// Add global error handler for unhandled promise rejections
window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
    // Prevent the default browser behavior
    event.preventDefault();
});

// Event Listeners
document.getElementById('join-btn').addEventListener('click', joinStream);
document.getElementById('leave-btn').addEventListener('click', leaveAndRemoveLocalStream);
document.getElementById('mic-btn').addEventListener('click', toggleMic);
document.getElementById('camera-btn').addEventListener('click', toggleCamera);
document.getElementById('switch-camera-btn').addEventListener('click', switchCamera);

// Window resize handler for responsive grid
window.addEventListener('resize', () => {
    adjustGridForScreenSize();
    updateGridLayout();
});

// Orientation change handler for mobile devices
window.addEventListener('orientationchange', () => {
    setTimeout(() => {
        adjustGridForScreenSize();
        updateGridLayout();
        checkScrollOverflow();
    }, 100);
});

// Scroll event listener for video streams
document.addEventListener('DOMContentLoaded', () => {
    const videoStreams = document.getElementById('video-streams');
    if (videoStreams) {
        videoStreams.addEventListener('scroll', () => {
            // Fade scroll indicator when user starts scrolling
            if (videoStreams.classList.contains('has-overflow')) {
                videoStreams.style.setProperty('--scroll-indicator-opacity', '0.3');
                setTimeout(() => {
                    videoStreams.style.setProperty('--scroll-indicator-opacity', '0.7');
                }, 2000);
            }
        });

        // Initial check
        setTimeout(() => {
            checkScrollOverflow();
        }, 500);
    }
});

// Add touch gesture support for better mobile experience
let touchStartY = 0;
let touchStartX = 0;

document.addEventListener('touchstart', (e) => {
    touchStartY = e.touches[0].clientY;
    touchStartX = e.touches[0].clientX;
});

document.addEventListener('touchmove', (e) => {
    const videoStreams = document.getElementById('video-streams');
    if (videoStreams && videoStreams.contains(e.target)) {
        const touchY = e.touches[0].clientY;
        const touchX = e.touches[0].clientX;
        const deltaY = touchStartY - touchY;
        const deltaX = touchStartX - touchX;

        // Prevent default scrolling if we're in the video streams area
        if (Math.abs(deltaY) > 10 || Math.abs(deltaX) > 10) {
            e.preventDefault();
        }
    }
}, { passive: false });

// Theme toggle event listener
document.getElementById('theme-toggle').addEventListener('click', toggleThemeMenu);

// Theme option event listeners
document.querySelectorAll('.theme-option').forEach(option => {
    option.addEventListener('click', () => {
        const theme = option.dataset.theme;
        applyTheme(theme);
        updateActiveThemeOption();
        document.getElementById('theme-menu').classList.remove('show');
    });
});
